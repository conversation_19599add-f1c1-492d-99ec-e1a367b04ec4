import { <PERSON>, Post, Body, HttpStatus } from '@nestjs/common';
import { time } from 'console';
//import { OnBoardingModel } from '../models/onboarding.model';
import { TasksService } from '../service/task.service';

@Controller('cache-refresh')
export class CacheRefreshController {
  constructor(private chartService: TasksService) {}

  @Post('load-cache')
  async refreshCache(@Body() data: Partial<any>) {
    return {
      statusCode: HttpStatus.OK,
      data: await this.chartService.stopCronJob(),
    };
  }
  @Post('add')
  async addNewJob(@Body() data: Partial<any>)  : Promise<any> {
    return {
      statusCode: HttpStatus.OK,
      data: await this.chartService.addNewSecondsJob(data.jobName, data.time),
    };
  }

  @Post('get')
  async getCronJobs(@Body() data: Partial<any>) {
    return {
      statusCode: HttpStatus.OK,
      data: await this.chartService.getCronJobs(),
    };
  }
  @Post('delete')
  async deleteAllCronJobs(@Body() data: Partial<any>) {
    return {
      statusCode: HttpStatus.OK,
      data: await this.chartService.deleteAllCronJobs(),
    };
  }
}
