import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
/* Commented by Sudeep for removing mongo db reference*/
// import { UserInfo } from './models/user-info.model';
import { ChartBuilderService } from './service/chart-builder.service';
import { ChartBuilderEntity } from './entities/chartdata.entity';

import * as dotenv from 'dotenv';
dotenv.config();

@Module({
  imports: [
      TypeOrmModule.forFeature([ChartBuilderEntity]),
  ],
  providers: [ChartBuilderService],
  exports: [ChartBuilderService]
})
export class ChartBuilderModule { }
