const fs = require('fs');
const path = require('path');

const { promises: Fs } = require('fs')

export class CacheHelper {
  
  static saveChartDataTofile(chartData, client_id) {
    let data = JSON.stringify(chartData);
    fs.writeFileSync(
      path.join(__dirname, process.env.CACHE_PATH+'chart-data-' + client_id + '.json'),
      data,
    );
  }

  static saveNewChartDataTofile(chartData, client_id) {
    let jsonChartData = JSON.stringify(chartData);
    let data = '[{"cb_json":'+ ((chartData.length>0)? jsonChartData:'null') +'}]';
    fs.writeFileSync(
      path.join(__dirname, process.env.CACHE_PATH+'chart-data-' + client_id + 'New.json'),
      data,
    );
  }

  static saveNewMetaDataTofile(metaData, client_id) {
    let jsonMetaData = JSON.stringify(metaData);
    let data = '[{"cb_json":'+ ((metaData.length>0)? jsonMetaData:'null') +'}]';
    fs.writeFileSync(
      path.join(__dirname, process.env.CACHE_PATH+'meta-data-' + client_id + 'New.json'),
      data,
    );
  }


  static saveMetaDataTofile(chartData, client_id) {
    let data = JSON.stringify(chartData);
    fs.writeFileSync(
      path.join(__dirname, process.env.CACHE_PATH+'meta-data-' + client_id + '.json'),
      data,
    );
  }

  static saveCalenderTofile(chartData) {
    let data = JSON.stringify(chartData);
    fs.writeFileSync(path.join(__dirname, process.env.CACHE_PATH+'calender-data.json'), data);
  }
  
}
