import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';
import * as dotenv from 'dotenv';
import * as https from 'https';
import * as http from 'http';
import * as fs from 'fs';
import * as express from 'express';
import { ExpressAdapter } from '@nestjs/platform-express';
import { join } from 'path';
dotenv.config();
// For  local 
 async function bootstrap() {
   const app = await NestFactory.create(AppModule);
   app.use(cookieParser());
  // app.enableCors();
   //enable cors for all origins and browsers
   app.enableCors({
     origin: '*',
     methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
     allowedHeaders: 'Content-Type, Authorization, X-Requested-With',
     credentials: true,
   });
   await app.listen(process.env.PORT || 4000);
 }


//In Server

// async function bootstrap() {
//   const privateKey = fs.readFileSync(join(__dirname, '..', 'certs', 'private.key'))
//   const certificate = fs.readFileSync(join(__dirname, '..', 'certs', '__lacritz_ai.crt'))
//   const httpsOptions = {
//     key: privateKey,
//     cert: certificate,
//   };
//   const app = await NestFactory.create(AppModule, {
//     httpsOptions,
//     cors: true
//   });
//   await app.listen(process.env.PORT || 4000);

//   app.use(cookieParser());
//   // app.enableCors();
//   app.enableCors({
//         origin: '*',
//         methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
//         allowedHeaders: 'Content-Type, Authorization, X-Requested-With',
//         credentials: true,
//       });

// }
bootstrap();
