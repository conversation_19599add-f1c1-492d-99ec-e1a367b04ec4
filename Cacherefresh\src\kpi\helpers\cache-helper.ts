
const path = require('path');
const fsPromises = require('fs/promises');
export class CacheHelper {
  static async saveKPIDataTofile(kpiData: any, client_id: string) {
    let data = JSON.stringify(kpiData);
    let filePath = path.join(
      __dirname,
      process.env.KPI_CACHE_PATH + client_id + '_KPI.json',
    );
    await this.writeFile(filePath, data);
    
  }
  static async writeFile(path: string, data: any) {
    try {
      await fsPromises.writeFile(path, data);
    } catch (err) {
      throw Error(err);
    }
  }
}
