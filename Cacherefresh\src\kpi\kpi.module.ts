import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
/* Commented by Sudeep for removing mongo db reference*/
// import { UserInfo } from './models/user-info.model';
import { KPIService } from './service/kpi.service';
import { KPIEntity } from './entities/kpi.entity';
import { RedisModule } from '../redis/redis.module';

import * as dotenv from 'dotenv';
dotenv.config();

@Module({
  imports: [
      TypeOrmModule.forFeature([KPIEntity]),
      RedisModule,
  ],
  providers: [KPIService],
  exports: [KPIService]
})
export class KPIModule { }
