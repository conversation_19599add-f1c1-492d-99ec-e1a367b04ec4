import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';
import { DateTime } from 'luxon';

@Injectable()
export class AlertService implements OnModuleInit {
  private readonly logger = new Logger(AlertService.name);

  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {}

  onModuleInit() {
    this.logger.log(
      '🚀 Alert service initialized. Scheduler will run every minute.',
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async checkAlerts() {
    try {
      const query = `
        SELECT * FROM ${process.env.DB_Postgres_AGENT_SCHEMA}.alerting_agent_alerts
        WHERE alert_status = 'ACTIVE'
      `;

      const allActiveAlerts = await this.entityManager.query(query);
      const matchingAlerts: any[] = [];

      for (const alert of allActiveAlerts) {
        const { alert_time, user_timezone } = alert;

        const currentTimeInZone = DateTime.now().setZone(user_timezone);
        const formattedTime = currentTimeInZone
          .set({ second: 0 })
          .toFormat('HH:mm:ss');

        if (formattedTime === alert_time) {
          matchingAlerts.push(alert);
        }
      }

      if (matchingAlerts.length === 0) {
        this.logger.log('No timezone-matching alerts found this minute.');
        return;
      }

      this.logger.log(
        `✅ Found ${matchingAlerts.length} timezone-matched alerts. Sending to dashboard...`,
      );

      await this.sendAlertsToDashboard(matchingAlerts);
    } catch (error) {
      this.logger.error('Error checking alerts:', error.message);
    }
  }

  private async sendAlertsToDashboard(alerts: any[]) {
    const dashboardApi = process.env.DASHBOARD_API_URL;

    try {
      await axios.post(`${dashboardApi}/alerting-agent/alerts/check-criteria`, {
        alerts,
      });

      this.logger.log(
        `✅ ${alerts.length} Alerts sent to dashboard successfully.`,
      );
    } catch (error) {
      if (axios.isAxiosError(error)) {
        this.logger.error(
          `❌ Failed to send alerts to dashboard: ${error.message}`,
        );
        if (error.response) {
          this.logger.error(
            `Dashboard responded with status ${
              error.response.status
            }: ${JSON.stringify(error.response.data)}`,
          );
        }
      } else {
        this.logger.error(`Unknown error: ${error}`);
      }
    }
  }
}
