import { TypeOrmModuleOptions } from "@nestjs/typeorm";
import * as dotenv from 'dotenv';
import { join } from "path";

//dotenv.config();

export const postgresOrmConfig: TypeOrmModuleOptions = {
    type: 'postgres',
    host: process.env.DB_Postgres_HOST,
    username: process.env.DB_Postgres_USER,
    password: process.env.DB_Postgres_PASSWORD,
    database: process.env.DB_Postgres_DATABASE,
    port: parseInt(process.env.DB_Postgres_PORT, 10),
    entities: [join(__dirname, '/**/*.entity{.ts,.js}')],
    autoLoadEntities: true,
}