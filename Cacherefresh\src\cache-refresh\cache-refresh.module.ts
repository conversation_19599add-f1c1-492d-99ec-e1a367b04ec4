import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TasksService } from './service/task.service';
import { CacheRefreshController } from './controller/cache-refresh.controller';
import { ChartBuilderModule } from '../chartbuilder/chart-builder.module';
import { ClientDataEntity } from './entities/client-data.entity';
import * as dotenv from 'dotenv';
import { KPIModule } from '../kpi/kpi.module';
dotenv.config();

@Module({
  imports: [TypeOrmModule.forFeature([ClientDataEntity]), ChartBuilderModule, KPIModule],
  providers: [TasksService],
  exports: [TasksService],
  controllers: [CacheRefreshController],
})
export class CacheRefreshModule {}
