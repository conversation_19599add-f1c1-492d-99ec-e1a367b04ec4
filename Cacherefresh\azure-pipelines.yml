trigger:
   branches:
      include:
         - develop

resources:
   repositories:
      - repository: flable_configurations
        ref: flable_configurations
        type: git
        name: flable_configurations

parameters:
   - name: env
     displayName: Deployment Environment
     type: string
     default: develop
     values:
        - develop
        - master
        - master_de

variables:
   - name: dockerfilePath
     value: '$(Build.ArtifactStagingDirectory)/Dockerfile'
   - template: ./vars.yml
     parameters:
        env: ${{ parameters.env }}
   - name: vmImageName
     value: 'ubuntu-latest'

stages:
   - stage: Build
     displayName: Build and push stage
     jobs:
        - job: Build
          displayName: Build
          pool:
             vmImage: $(vmImageName)
          steps:
             - checkout: self
             - checkout: flable_configurations

             - task: CopyFiles@2
               displayName: 'Copying Application files'
               inputs:
                  SourceFolder: '$(Agent.BuildDirectory)/s/$(Build.Repository.Name)'
                  Contents: '**'
                  TargetFolder: '$(Build.ArtifactStagingDirectory)/'

             - task: CopyFiles@2
               displayName: 'Copying Configurations and environment'
               inputs:
                  SourceFolder: '$(Agent.BuildDirectory)/s/flable_configurations/$(Build.Repository.Name)/${{ parameters.env }}/'
                  Contents: '**'
                  TargetFolder: '$(Build.ArtifactStagingDirectory)/'
                  OverWrite: true

             - task: CopyFiles@2
               displayName: 'Copying Certificates'
               inputs:
                  SourceFolder: '$(Agent.BuildDirectory)/s/flable_configurations/certs/'
                  Contents: '*'
                  TargetFolder: '$(Build.ArtifactStagingDirectory)/certs/'
                  OverWrite: true

             - task: AzureKeyVault@2
               inputs:
                  azureSubscription: ${{ variables.serviceConnection }}
                  KeyVaultName: ${{ variables.keyvault }}
                  SecretsFilter: '*'
                  RunAsPreJob: false

             - task: CmdLine@2
               inputs:
                  script: |
                     sed -i 's/postgres-pwd/$(postgres-pwd)/g' $(Build.ArtifactStagingDirectory)/.env
                     sed -i "s/redis-pass/$(redis-pass)/g" $(Build.ArtifactStagingDirectory)/.env
               displayName: 'Secret pulled from Azure Key Vault'

             - script: |
                  echo "Listing files in $(Build.ArtifactStagingDirectory):"
                  ls -ltar $(Build.ArtifactStagingDirectory)/
                  echo "Listing files in $(Build.ArtifactStagingDirectory)/certs/:"
                  ls -ltar $(Build.ArtifactStagingDirectory)/certs/
               displayName: 'List files in Artifact Staging Directory'

             - task: Docker@2
               displayName: Build image to container registry
               inputs:
                  command: build
                  repository: ${{ variables.imagerepository }}
                  dockerfile: $(dockerfilePath)
                  containerRegistry: ${{ variables.dockerRegistryServiceConnection }}
                  arguments: '--build-arg TZ=${{ variables.dockertimezone }}'
                  tags: |
                     latest
                     $(Build.BuildId)

             - task: Docker@2
               displayName: Push Docker image
               inputs:
                  command: push
                  repository: ${{ variables.imagerepository }}
                  tags: |
                    latest
                    $(Build.BuildId)
                  containerRegistry: ${{ variables.dockerRegistryServiceConnection }}        

   - stage: Deploy
     displayName: 'Deploy'
     jobs:
        - deployment: 'applyinfra'
          environment: $(environmentDeploymentGate)
          displayName: 'Deploy to App'
          strategy:
             runOnce:
                deploy:
                   steps:
                      - task: AzureContainerApps@1
                        displayName: 'Azure Container Apps Deploy'
                        inputs:
                           azureSubscription: ${{ variables.serviceConnection }}
                           acrName: ${{ variables.containerRegistryName }}
                           imageToDeploy: '${{ variables.containerRegistry }}/${{ variables.imagerepository }}:$(Build.BuildId)'
                           containerAppName: ${{ variables.containerAppName }}
                           resourceGroup: ${{ variables.resourceGroup }}
                           containerAppEnvironment: ${{ variables.containerAppEnvironment }}
                           disableTelemetry: false
