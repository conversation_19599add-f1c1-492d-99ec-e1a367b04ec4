export interface MetricReport {
  clientId: string;
  title: string;
  time: string;
  frequency: string;
  interval: string;
  start_date: string;
  end_date: string;
  date: string;
  emails: string;
  kpis: string;
  username: string;
  reportId: string;
  report_id?: string;
  client_id?: string;
  timezone_name?: string;
  is_subscribed: string;
  is_auto_report: boolean;
}

export interface MetricKPI {
  category: string;
  kpi: string;
  kpi_display_name: string;
}
export interface SettingsBaseModel {
  client_id: string;
  language: string;
  timezone: string;
  industry: string;
  category: string;
  timezone_name: string;
}

export type FetchTimezoneRequest = Pick<SettingsBaseModel, 'client_id'>;

export type FetchTimezoneResponse = Omit<
  SettingsBaseModel,
  'client_id' | 'language | industry | category'
>;
