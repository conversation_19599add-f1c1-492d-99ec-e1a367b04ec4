import { Entity, PrimaryGeneratedColumn, Column, Timestamp } from 'typeorm';

@Entity({ name: 'temp_alerts' })
export class AlertEntity {
  @PrimaryGeneratedColumn()
  alert_id: number;

  @Column({ type: 'varchar', length: 50 })
  client_id: string;

  @Column({ type: 'varchar', length: 50 })
  chat_id: string;

  @Column({ type: 'varchar', length: 200 })
  alert_name: string;

  @Column({ type: 'text' })
  alert_description: string;

  @Column({ type: 'text' })
  alert_instruction: string;

  @Column({ type: 'text' })
  recipients: string;

  @Column({ type: 'varchar', length: 50 })
  channel: string;

  @Column({ type: 'varchar', length: 200 })
  campaign_name: string;

  @Column({ type: 'varchar', length: 50 })
  campaign_id: string;

  @Column({ type: 'varchar', length: 100 })
  kpi: string;

  @Column({ type: 'varchar', length: 50 })
  trend: string;

  @Column({ type: 'varchar', length: 50 })
  value: string;

  @Column({ type: 'varchar', length: 50 })
  value_type: string;

  @Column({ type: 'varchar', length: 50 })
  comparison: string;

  @Column({ type: 'varchar', length: 50 })
  comparison_type: string;

  @Column({ type: 'varchar', length: 20 })
  alert_status: string;

  @Column({ type: 'time without time zone' })
  alert_time: string; // Format: 09:00:00

  @Column({ type: 'timestamp without time zone' })
  created_at: Timestamp;

  @Column({ type: 'timestamp without time zone' })
  updated_at: Timestamp;
}
