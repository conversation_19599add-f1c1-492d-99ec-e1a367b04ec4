import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';

@Injectable()
export class SubscriptionService implements OnModuleInit {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {}

  onModuleInit() {
    this.logger.log(
      '🚀 Subscription service initialized. Scheduler will run every day at midnight.',
    );
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async expireSubscription() {
    try {

       const subscriptions = await this.entityManager.query(`
            SELECT subscription_id, client_id
            FROM subscription.subscription
            WHERE auto_renew = false
              AND is_active = true
              AND subs_end_dt <= now()
        `);

        this.logger.log(`Found ${subscriptions.length} subscriptions to expire`);

        for (const sub of subscriptions) {
            await this.entityManager.query(
                `CALL ${process.env.DB_Postgres_SUBSCRIPTION_SCHEMA}.q_expire_cancel_subs($1, $2)`,
                [sub.subscription_id, sub.client_id]
            );
            this.logger.log(`Expired subscription: ${sub.subscription_id}`);
        }

        this.logger.log('Auto-expire subscriptions task completed');

    } catch (error) {
      this.logger.error('Error running auto-expire subscriptions task : ', error.message);
    }
  }
}
