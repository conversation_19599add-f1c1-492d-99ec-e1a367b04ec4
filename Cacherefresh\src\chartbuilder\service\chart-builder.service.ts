import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChartBuilderEntity } from '../entities/chartdata.entity';
import { CacheHelper } from '../helpers/cache-helper';

const fs = require('fs');
const path = require('path');

@Injectable()
export class ChartBuilderService {
  constructor(
    @InjectRepository(ChartBuilderEntity)
    private usersRepository: Repository<
      ChartBuilderEntity
    > /* Commented by Sudeep for removing mongo db reference*/, // @InjectRepository (UsersEntity) private userRepository: Repository<UsersEntity>, // @InjectRepository (UserInfo) private userInfoModel: Repository<UserInfo>
  ) {}

  
  async getChartData(clientID: string) {
    console.log('*******start time ', new Date())
    const q2 = `CALL ${process.env.DB_Postgres_SCHEMA}.q_imc_event_master_list('${clientID}')`;
    const data2 = await this.usersRepository.query(q2);
    // const q3 = `CALL ${process.env.DB_Postgres_SCHEMA}.q_imc_data_driver('${clientID}')`;
    // const data3 = await this.usersRepository.query(q3);
     
    const query: any = "select * from " + process.env.DB_Postgres_SCHEMA + ".fn_imc_get('" + clientID + "')";
    try {
      const data = await this.usersRepository.query(query);
      this.restructureChartDataJson(data, clientID);
      CacheHelper.saveChartDataTofile(data, clientID);
    } catch (error) {
      console.log('*******error', error, query)
    }
    const metaquery: any = "select * from " + process.env.DB_Postgres_SCHEMA + ".fn_imc_metadata_get('" + clientID + "')";
    const meatadata = await this.usersRepository.query(metaquery);
    this.restructureMetaDataJson(meatadata, clientID);
    CacheHelper.saveMetaDataTofile(meatadata, clientID);
    const calenderquery: any =
      'select * from ' + process.env.DB_Postgres_SCHEMA + '.fn_imc_meta_global_calendar_data()';
    const calenderdata = await this.usersRepository.query(calenderquery);
    CacheHelper.saveCalenderTofile(calenderdata);
    console.log('*******end time ', new Date())
    return {};
  }

  private restructureMetaDataJson(data: any, clientID: string) {
    console.log('*******client ', clientID, ' meta data:****** ');
    var newData = [];
    if (data[0].cb_json) {
      for (var entry of data[0].cb_json) {
        var newField = entry.field;
        var newDescription = entry.description;
        if (entry.field === 'hour' || entry.field === 'referrer') {
          newField = 'session_details.' + entry.field;
          //newDescription = entry.description + '(Session Details)';
        }
        newData.push({
          field: newField,
          datatype: entry.datatype,
          description: entry.description,
          event_type: entry.event_type,
          category: entry.category
        });
      }
    }
    CacheHelper.saveNewMetaDataTofile(newData, clientID);
  }
  
  private restructureChartDataJson(data: any, clientID: string) {
    console.log('*******client ', clientID, ' data:****** ');
    var newData = [];
    if (data[0].cb_json) {
      for (var oldEntry of data[0].cb_json) {
        var entryExists = false;
        for (var newEntry of newData) {
          if (newEntry.date=== oldEntry.date && newEntry.user_id === oldEntry.user_id.split('|')[0]) {
            entryExists = this.checkForExistingUserId(newEntry, oldEntry);
          } else if (newEntry.date === oldEntry.date) {
            entryExists = this.checkForExistingSessionIds(newEntry, oldEntry);
          }
          if (entryExists) {
            break;
          }
        }
        if (!entryExists) {
          this.pushToNewJson(newData, oldEntry);
        }
      }
    }
    CacheHelper.saveNewChartDataTofile(newData, clientID);
  }

  private checkForExistingUserId(newEntry: any, oldEntry: any) {
    var entryExists = true;
    var sessionExists = false;
    for (var session of newEntry.session_details) {
      if (session.session_id === oldEntry.session_id && session.hour === oldEntry.hour) {
        sessionExists = true;
        break;
      }
    }
    if (!sessionExists) {
      newEntry.session_details.push({
        session_id: oldEntry.session_id,
        hour: oldEntry.hour,
        referrer: oldEntry.referrer,
      });
    }
    if (newEntry.event_data == null && oldEntry.event_data != null) {
      newEntry.event_data = oldEntry.event_data;
    }
    else if (newEntry.event_data != null && oldEntry.event_data != null) {
      let newEntryData = JSON.parse(newEntry.event_data);
      let oldEntryData = JSON.parse(oldEntry.event_data);
      newEntry.event_data = JSON.stringify([...oldEntryData, ...newEntryData])
    }
    return entryExists;
  }

  private checkForExistingSessionIds(newEntry, oldEntry) {
    var entryExists = false;
    for (var session of newEntry.session_details) {
      if (session.session_id === oldEntry.session_id) {
        entryExists = true;
        if (newEntry.event_data == null && oldEntry.event_data != null) {
          newEntry.event_data = oldEntry.event_data;
        }
        else if (newEntry.event_data != null && oldEntry.event_data != null) {
          let newEntryData = JSON.parse(newEntry.event_data);
          let oldEntryData = JSON.parse(oldEntry.event_data);
          newEntry.event_data = JSON.stringify([...oldEntryData, ...newEntryData])
        }
        if (newEntry.date > oldEntry.date || newEntry.week_start_date > oldEntry.week_start_date || newEntry.month_start_date > oldEntry.month_start_date || newEntry.quarter_start_date > oldEntry.quarter_start_date) {
          session.hour = oldEntry.hour;
          newEntry.date = oldEntry.date;
          newEntry.week_start_date = oldEntry.week_start_date;
          newEntry.month_start_date = oldEntry.month_start_date;
          newEntry.quarter_start_date = oldEntry.quarter_start_date;
          newEntry.user_id = oldEntry.user_id.split('|')[0];
          session.referrer = oldEntry.referrer;
          newEntry.browser = oldEntry.browser;
          newEntry.operating_system = oldEntry.operating_system;
          newEntry.device_category = oldEntry.device_category;
          newEntry.channel = oldEntry.channel;
          newEntry.country = oldEntry.country;
          newEntry.region = oldEntry.region;
          newEntry.city = oldEntry.city;
          newEntry.event_data = oldEntry.event_data;
        }
        break;
      }
    }
    return entryExists;
  }

  private pushToNewJson(newData: any[], oldEntry: any) {
    newData.push({
      date: oldEntry.date,
      week_start_date: oldEntry.week_start_date,
      month_start_date: oldEntry.month_start_date,
      quarter_start_date: oldEntry.quarter_start_date,
      user_id: oldEntry.user_id.split('|')[0],
      session_details: [{
        session_id: oldEntry.session_id,
        hour: oldEntry.hour,
        referrer: oldEntry.referrer
      }],
      browser: oldEntry.browser,
      operating_system: oldEntry.operating_system,
      device_category: oldEntry.device_category,
      channel: oldEntry.channel,
      country: oldEntry.country,
      region: oldEntry.region,
      city: oldEntry.city,
      event_data: oldEntry.event_data
    });
  }

 
}
