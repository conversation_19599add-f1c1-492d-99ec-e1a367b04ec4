import { Injectable, OnModuleDestroy } from '@nestjs/common';
import Redis, { RedisOptions } from 'ioredis';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private client: Redis;

  constructor() {
    this.client = new Redis({
      host: process.env.REDIS_HOST,
      port: Number(process.env.REDIS_PORT),
      password: process.env.REDIS_PASS,
    } as RedisOptions);

    this.client.on('connect', () => {
      console.log('✅ Redis connected');
    });

    this.client.on('error', (err) => {
      console.error('❌ Redis error:', err);
    });
  }

  private secondsUntilMidnight(): number {
    const now = new Date();
    const midnight = new Date(now);
    midnight.setHours(24, 0, 0, 0);
    return Math.floor((midnight.getTime() - now.getTime()) / 1000);
  }

  private withSuffix(key: string): string {
    return`${process.env.REDIS_PREFIX}:${key}`;
  }

  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(this.withSuffix(key));
    } catch (err) {
      console.error(`Redis GET error for key "${key}":`, err);
      return null;
    }
  }

  async set(
    key: string,
    value: string,
    options?: { expireAtMidnight?: boolean; ttlInSeconds?: number }
  ): Promise<void> {
    try {
      const redisKey = this.withSuffix(key);
      if (options?.expireAtMidnight) {
        const ttl = this.secondsUntilMidnight();
        await this.client.set(redisKey, value, 'EX', ttl);
      } else if (options?.ttlInSeconds) {
        await this.client.set(redisKey, value, 'EX', options.ttlInSeconds);
      } else {
        await this.client.set(redisKey, value);
      }
    } catch (err) {
      console.error(`Redis SET error for key "${key}":`, err);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.client.del(this.withSuffix(key));
    } catch (err) {
      console.error(`Redis DEL error for key "${key}":`, err);
    }
  }

  async onModuleDestroy() {
    await this.client.quit();
  }
} 