// import { Injectable, Logger } from '@nestjs/common';
// import { Cron } from '@nestjs/schedule';

// @Injectable()
// export class TasksService {
//   private readonly logger = new Logger(TasksService.name);

//   @Cron('45 * * * * *')
//   handleCron() {
//     this.logger.debug('Called when the current second is 45');
//   }
// }

import { InjectRepository } from '@nestjs/typeorm';
const fsPromises = require('fs/promises');
const path = require('path');
import { Repository } from 'typeorm';
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Cron, SchedulerRegistry, CronExpression } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { ChartBuilderService } from '../../chartbuilder/service/chart-builder.service';
import { ClientDataEntity } from '../entities/client-data.entity';
import { KPIService } from '../../kpi/service/kpi.service';
import { MetricReport } from './interface';

@Injectable()
export class TasksService implements OnModuleInit {
  private readonly logger = new Logger(TasksService.name);
  constructor(
    private schedulerRegistry: SchedulerRegistry,
    @InjectRepository(ClientDataEntity)
    private clientDataRepository: Repository<ClientDataEntity>,
    private chartService: ChartBuilderService,
    private kpiService: KPIService,
  ) {}

  async onModuleInit(): Promise<void> {
    //console.log(`Initialization...`);
    let KPIfolderPath = path.join(__dirname, process.env.KPI_CACHE_PATH);
    this.createFolderIfNotExists(KPIfolderPath);
    this.createSchedules();
    //this.createSchedules();
    // console.log('here')
  }
  async createFolderIfNotExists(path: string) {
    try {
      await fsPromises.access(path);
    } catch (err) {
      console.log(path, 'does not exist, creating directory');
      try {
        await fsPromises.mkdir(path);
      } catch (err) {
        console.log(path, 'could not make the directory', err);
      }
    }
  }
  async createSchedules() {
    this.logger.log('creating schedules');
    await this.syncAutoReportRecords();
    this.syncEmailReportJobs();
    this.addKPIJob(CronExpression.EVERY_6_HOURS); //  6 hours here

    //  Weekly auto report sync every Monday at 6:00 AM
    const autoReportJob = new CronJob('30 9 * * 1', async () => {
      await this.syncAutoReportRecords();
    });

    this.schedulerRegistry.addCronJob('autoReportSync', autoReportJob);
    autoReportJob.start();
  }
  // @Cron('* * * * * *')
  // triggerCronJob() {
  //   //console.log('Calling the method every second');
  // }
  async getClientData() {
    // insert/update chart details and  update the dashboard id if any and update chartid
    // requestParams.chartId = 1;
    // requestParams.dashboard_id = 2;

    const query: any = 'select * from config.fn_get_active_accounts()';
    const data = await this.clientDataRepository.query(query);

    return data;
  }
  addNewMinutesJob(jobName: string, time: string) {
    const job = new CronJob(`0 */${time} * * * *`, () => {
      //console.log(`time (${time}) for job ${jobName} to run!`);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();

    //console.log(`Job ${jobName} added for every ${time} seconds`);
  }
  async addKPIJob(time: string): Promise<void> {
    const syncKPIData = async () => {
      const kpiClients = await this.kpiService.getKPIClients();
      const uniqKpiClients = [...new Set(kpiClients)];
      uniqKpiClients.forEach(async (client: string) => {
        await this.kpiService.getKPIData(client);
      });
    };
    syncKPIData();
    const job = new CronJob(time, syncKPIData);

    this.schedulerRegistry.addCronJob('KPI', job);
    job.start();
  }

  async getAllTasks(): Promise<MetricReport[]> {
    const q = `SELECT e.*, l.timezone, l.timezone_name 
    FROM ${process.env.DB_Postgres_SCHEMA}.email_report e 
    INNER JOIN ${process.env.DB_Postgres_SCHEMA}.language_and_region l on e.client_id = l.client_id
`;
    try {
      const data = await this.clientDataRepository.query(q);

      return data;
    } catch (error) {
      console.log('*******error', error, q);
      return [];
    }
  }
  private dayOfWeekToNumber(day: string): number {
    const daysMap: Record<string, number> = {
      sunday: 0,
      monday: 1,
      tuesday: 2,
      wednesday: 3,
      thursday: 4,
      friday: 5,
      saturday: 6,
    };
    return daysMap[day.toLowerCase()] ?? -1; // Return -1 if invalid
  }
  // Check if today is the last day of the month
  private isLastDayOfMonth(): boolean {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    return tomorrow.getDate() === 1; // If tomorrow is 1st, today is last day
  }

  // Get the first valid weekday after the start date
  private getFirstValidWeekday(startDate: string, targetDay: number): Date {
    let start = new Date(startDate);
    let dayOfWeek = start.getDay(); // Get numeric day (0=Sunday, 1=Monday, ..., 6=Saturday)

    // Move to the next occurrence of the target weekday
    let daysToAdd = (targetDay - dayOfWeek + 7) % 7;
    if (daysToAdd === 0) daysToAdd = 7; // Ensure it's a future date
    start.setDate(start.getDate() + daysToAdd);

    return start; // First valid target weekday after start date
  }

  // Check if today matches the bi-weekly or tri-weekly schedule
  private isValidWeekly(
    startDate: string,
    intervalWeeks: number,
    targetDay: number,
  ): boolean {
    const firstValid = this.getFirstValidWeekday(startDate, targetDay);
    const today = new Date();

    // Find the number of days from the first valid weekday
    const diffDays = Math.floor(
      (today.getTime() - firstValid.getTime()) / (1000 * 60 * 60 * 24),
    );

    return diffDays >= 0 && diffDays % (intervalWeeks * 7) === 0;
  }
  // Check if today matches the N-day interval (for daily reports)
  private isValidDaily(startDate: string, intervalDays: number): boolean {
    const start = new Date(startDate);
    const today = new Date();
    const diffDays = Math.floor(
      (today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
    );
    return diffDays >= 0 && diffDays % intervalDays === 0;
  }
  // Check if today matches the start, mid, or end of the month for multi-month reports
  private isValidMultiMonth(
    startDate: string,
    intervalMonths: number,
    monthType: string,
  ): boolean {
    const today = new Date();
    const start = new Date(startDate);

    if ((today.getMonth() - start.getMonth()) % intervalMonths !== 0) {
      return false; // Not in the correct month cycle
    }

    const day = today.getDate();
    if (monthType === 'First' && day === 1) return true;
    if (monthType === 'Mid' && day === 15) return true;
    if (monthType === 'Last' && this.isLastDayOfMonth()) return true;

    return false;
  }
  async addEmailReportJob(report: MetricReport): Promise<void> {
    const start = new Date(report.start_date);
    const end =
      report.end_date === 'infinity'
        ? new Date(8640000000000000)
        : new Date(report.end_date);
    const now = new Date();
    const name = report.title;
    // If current date is not within start and end date, skip
    if (now < start || now > end) {
      console.log(`Skipping ${name}: Outside valid date range.`);
      return;
    }

    const [hr, min] = report.time.split(':');
    const job = new CronJob(
      `${min} ${hr} * * ${
        report.frequency == 'week' ? new Date(report.date).getDay() : '*'
      }`,
      () => {
        this.kpiService.sendEmailReport(report);
      },
    );

    this.schedulerRegistry.addCronJob(`ER-${report.report_id}`, job);
    //console.log(`**********ADDED JOB ER-${report.report_id} ************`);
    this.logger.log(`Added Job ER-${report.report_id}`);
    job.start();
  }

  async getAutoReportRecord(
    clientId: string,
    user_id: string,
  ): Promise<{
    is_subscribed: boolean;
    is_auto_report: boolean;
    kpis: string;
  } | null> {
    const query = `
   SELECT is_subscribed, kpis
   FROM ${process.env.DB_Postgres_SCHEMA}.email_report
   WHERE client_id = $1 AND user_id = $2 AND is_auto_report = true
   LIMIT 1
   `;
    const res = await this.clientDataRepository.query(query, [
      clientId,
      user_id,
    ]);
    return res[0] || null;
  }

  // async getActiveUsersForClient(
  //   clientId: string,
  // ): Promise<{ userId: string; email: string; isAutoReportActive: boolean }[]> {
  //   // Replace with your actual DB query
  //   const query = `SELECT user_id, email FROM users WHERE client_id = $1 AND is_active = true`;
  //   return await this.clientDataRepository.query(query, [clientId]);
  // }

  async insertOrUpdateAutoReportRecord(
    clientId: string,
    userId: string,
    email: string,
  ) {
    const title = 'Agent Weekly Report';
    const frequency = 'week';
    const interval = '1 week, Monday';
    const start_date = new Date().toISOString();
    const end_date = 'infinity';
    const time = '11:00';
    const date = new Date().toISOString();
    const { activeKpis = [], activeCategories = [] } =
      (await this.kpiService.getMetaDataForAutoReport(clientId)) || {};
    const kpis = JSON.stringify(activeKpis);
    if (activeKpis.length === 0) return;
    //const username = this.kpiService.getUserName(clientId);
    const username = 'Flable user';
    const payload = {
      client_id: clientId,
      user_id: userId,
      title: title,
      username: username,
      frequency: frequency,
      interval: interval,
      start_date: start_date,
      end_date: end_date,
      date: date,
      time: time,
      emails: email,
      kpis: kpis,
      is_auto_report: true,
      is_subscribed: true,
    };

    const query = `CALL ${process.env.DB_Postgres_SCHEMA}.q_insert_email_report($1::JSONB)`;

    await this.clientDataRepository.query(query, [JSON.stringify(payload)]);
  }

  async syncAutoReportRecords() {
    this.logger.log('syncing auto reports records...');

    const clients = await this.kpiService.getActiveClients();

    const validClients = clients.filter(
      c => c.client_id && c.client_id !== 'undefined',
    );
    for (const { client_id } of validClients) {
      const users = await this.kpiService.getActiveUsersForClient(client_id);

      const { activeKpis = [], activeCategories = [] } =
        (await this.kpiService.getMetaDataForAutoReport(client_id)) || {};

      let bool = true;
      for (const user of users) {
        const existingReport = await this.getAutoReportRecord(
          client_id,
          user.user_id,
        );

        if (bool && existingReport && existingReport.is_subscribed) {
          this.logger.log('Hitting Xi endpoint for weekly report', client_id);
          this.kpiService.getWeeklyAutoReportFromXi(
            client_id,
            user.user_id,
            activeCategories,
          );
          bool = false;
        }

        if (!existingReport) {
          if (user.is_active && user.email_address && user.user_id) {
            await this.insertOrUpdateAutoReportRecord(
              client_id,
              user.user_id,
              user.email_address,
            );
          }
        }
      }
    }
  }

  // async markAutoReportUnsubscribed(clientId: string, userId: string) {
  //   const query = `
  //   UPDATE ${process.env.DB_Postgres_SCHEMA}.email_report
  //   SET is_subscribed = false, updated_at = NOW()
  //   WHERE client_id = $1 AND username = $2 AND is_auto_report = true;
  // `;
  //   await this.clientDataRepository.query(query, [clientId, userId]);
  // }

  async syncEmailReportJobs() {
    //const job = new CronJob(`*/${time} * * * * *`, () => {
    const job = new CronJob(`* * * * *`, async () => {
      // every minute

      const jobs = this.schedulerRegistry.getCronJobs();
      const emailReports = await this.getAllTasks();
      const allJobNames = Array.from(jobs.keys());

      //console.log('-----emailReports', emailReports);
      //console.log('alljobs', allJobNames);
      const toDeleteJobs = allJobNames
        .filter(job => job.includes('ER'))
        .filter(
          job =>
            !emailReports
              .filter(r => r.is_subscribed)
              .map(r => `ER-${r.report_id}`)
              .includes(job),
        );

      if (toDeleteJobs.length > 0) {
        toDeleteJobs.forEach(jobName => this.deleteJob(jobName));
      }
      emailReports
        ?.filter(r => r.is_subscribed)
        .filter(r => r.emails && r.emails.trim() !== '') // check emails present
        .filter(r => r.kpis && r.kpis.trim() !== '[]' && r.kpis.trim() !== '') //  check KPIs present
        .forEach(report => {
          let cronJobName = `ER-${report.report_id}`;
          let isJob = jobs.get(cronJobName);

          if (isJob) {
            let nextDates = isJob.nextDates(2);

            let isIncorrFreq =
              nextDates[1].toDate().getTime() -
                nextDates[0].toDate().getTime() >
                86400000 && report.frequency == 'day';

            let time = isJob.nextDate();
            let [hr, min] = report.time.split(':').map(Number);
            //console.log(hr, min, time.hours(), time.minutes())
            if (hr != time.hours() || min != time.minutes() || isIncorrFreq) {
              this.deleteJob(cronJobName);
              this.addEmailReportJob(report);
            }
          } else {
            this.addEmailReportJob(report);
          }
        });
    });

    this.schedulerRegistry.addCronJob('emailReport', job);
    job.start();
  }
  async addNewSecondsJob(client_id: string, time: string): Promise<void> {
    //const job = new CronJob(`*/${time} * * * * *`, () => {
    const job = new CronJob(`0 0 * * *`, () => {
      // every day at 00:00
      this.chartService.getChartData(client_id);
      //console.log(`time (${time}) for job ${client_id} to run!`);
    });

    this.schedulerRegistry.addCronJob(client_id, job);
    job.start();

    //console.log(`Job ${client_id} added for every ${time} seconds`);
  }

  deleteJob(jobName: string) {
    this.logger.log(`Deleting job ${jobName}`);
    this.schedulerRegistry.deleteCronJob(jobName);
  }

  getCronJobs() {
    const jobs = this.schedulerRegistry.getCronJobs();

    jobs.forEach((value, key, map) => {
      ////console.log('Value:', value);
      //console.log('key:', key);
    });
  }

  stopAllCronJobs() {
    const jobs = this.schedulerRegistry.getCronJobs();

    jobs.forEach((value, key, map) => {
      value.stop();
      //console.log('key:', key);
    });
  }
  startAllCronJobs() {
    const jobs = this.schedulerRegistry.getCronJobs();

    jobs.forEach((value, key, map) => {
      value.start();
      //console.log('key:', key);
    });
  }

  deleteAllCronJobs() {
    const jobs = this.schedulerRegistry.getCronJobs();

    jobs.forEach((value, key, map) => {
      this.schedulerRegistry.deleteCronJob(key);
      //console.log('key:', key);
    });
  }

  // @Cron(CronExpression.EVERY_30_SECONDS, { name: 'cache-refresh' })
  // triggerCronJobFiveSeconds() {
  //   //console.log('Calling the method every 30 seconds');
  // }

  stopCronJob() {
    const job = this.schedulerRegistry.getCronJob('cache-refresh');

    job.stop();
    //console.log(job.lastDate());
  }

  startCronJob() {
    const job = this.schedulerRegistry.getCronJob('cache-refresh');

    job.start();
    //console.log(job.lastDate());
  }

  addNewInterval(intervalName: string, intervalTime: number) {
    const callback = () => {
      //console.log(`Inteval ${intervalName} executing at time ${intervalTime}`);
    };

    const interval = setInterval(callback, intervalTime);
    this.schedulerRegistry.addInterval(intervalName, interval);
  }

  clearInterval() {
    const interval = this.schedulerRegistry.getInterval('messaging');
    clearInterval(interval);
  }

  deleteInterval(intervalName: string) {
    this.schedulerRegistry.deleteInterval(intervalName);
  }

  addNewTimeout(timeoutName: string, milliseconds: number) {
    const callback = () => {
      //console.log(`Timeout ${timeoutName} executing after ${milliseconds}`);
    };

    const timeout = setTimeout(callback, milliseconds);
    this.schedulerRegistry.addTimeout(timeoutName, timeout);
  }

  deleteTimeout(timeoutName: string) {
    this.schedulerRegistry.deleteTimeout(timeoutName);
  }
}
