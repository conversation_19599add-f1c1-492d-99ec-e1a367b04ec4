import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KPIEntity } from '../entities/kpi.entity';
import {
  FetchTimezoneRequest,
  FetchTimezoneResponse,
  MetricReport,
} from 'src/cache-refresh/service/interface';
import axios from 'axios';
import { RedisService } from '../../redis/redis.service';

@Injectable()
export class KPIService {
  private readonly logger = new Logger(KPIService.name);
  constructor(
    @InjectRepository(KPIEntity)
    private usersRepository: Repository<KPIEntity>,
    private readonly redisService: RedisService,
  ) {}
  async getKPIData(clientID: string) {
    const q = `SELECT * FROM ${process.env.DB_Postgres_SCHEMA}.fn_kpi_get('${clientID}')`;
    try {
      const data = await this.usersRepository.query(q);
      console.log('***************Got KPI_data******************  ', clientID);
      const cacheKey = `cache:kpiData:${clientID}`;
      const cache = await this.redisService.get(cacheKey);
      if (cache) {
        await this.redisService.del(cacheKey);
      }
      try {
        const pattern = `${process.env.REDIS_PREFIX}:dashboard:KPIs:${clientID}:*`;
        const keys = await this.redisService['client'].keys(pattern);
        if (keys && keys.length > 0) {
          await this.redisService['client'].del(...keys);
        }
      } catch (err) {
        console.error('Error deleting dash_data:KPI:* keys from Redis:', err);
      }
      await this.redisService.set(
        cacheKey,
        JSON.stringify(data[0]?.fn_kpi_get || []),
      );
    } catch (error) {
      console.error('*******error', error, q);
    }
  }
  async getKPIClients() {
    const q = `SELECT * FROM ${process.env.DB_Postgres_SCHEMA}.fn_get_kpi_clients()`;
    try {
      const data = await this.usersRepository.query(q);
      return data[0].fn_get_kpi_clients || [];
    } catch (error) {
      console.log('*******error', error, q);
    }
  }
  async getTimezone(
    payload: FetchTimezoneRequest,
  ): Promise<FetchTimezoneResponse | null> {
    const { client_id } = payload;
    const query = `SELECT timezone, timezone_name FROM ${process.env.DB_Postgres_SCHEMA}.language_and_region
          WHERE client_id = $1`;
    const result: FetchTimezoneResponse[] = await this.usersRepository.query(
      query,
      [client_id],
    );
    return result[0] ?? null;
  }
  async sendEmailReport(report: MetricReport) {
    try {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const sendMail = await axios.post(
        `${process.env.DASHBOARD_API_URL}/settings/reports/send`,
        {
          clientId: report.client_id,
          title: report.title,
          time: report.time,
          frequency: report.frequency,
          date: date.toISOString(),
          emails: report.emails,
          kpis: JSON.parse(report.kpis),
          username: report.username,
          reportId: report.report_id,
          is_auto_report: report.is_auto_report,
          is_subscribed: report.is_subscribed,
        },
      );
      console.log(
        report.title,
        '********MAIL SENT TO CLIENT***********',
        report.client_id,
      );
      return sendMail;
    } catch (error) {
      console.log('*******error', error);
    }
  }

  async getWeeklyAutoReportFromXi(
    clientId: string,
    userId: string,
    activeCategories: string[],
  ) {
    const url = `${process.env.DASHBOARD_API_URL}/settings/reports/fetchfromXi`;
    try {
      const response = await axios.post(url, {
        clientId,
        userId,
        activeCategories,
      });
    } catch (error) {
      this.logger.log('Error fetching Auto Report from Xi:', error.message);
      return;
    }
  }

  async getMetaDataForAutoReport(clientId: string): Promise<any> {
    const url = `${process.env.DASHBOARD_API_URL}/settings/reports/meta/auto/${clientId}`;
    try {
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      this.logger.log('Error fetching Auto Report Meta:', error.message);
      return {};
    }
  }

  async getActiveClients(): Promise<{ client_id: string }[]> {
    const query = `
  SELECT DISTINCT client_id
  FROM ${process.env.DB_Postgres_SCHEMA}.social_analytics
  WHERE is_active = true
`;
    return await this.usersRepository.query(query);
  }

  async getActiveUsersForClient(
    clientId: string,
  ): Promise<
    {
      user_id: string;
      email_address: string;
      is_active: boolean;
    }[]
  > {
    const query = `
  SELECT user_id, email_address, is_active
  FROM ${process.env.DB_Postgres_CONFIG_SCHEMA}.user_role
  WHERE client_id = $1
    AND is_active = true
    AND email_address IS NOT NULL
`;

    return await this.usersRepository.query(query, [clientId]);
  }
}
