{"name": "lacritz-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nodemon --config nodemon.json", "start:dev": "nest start --watch", "start:debug": "nodemon --config nodemon.json", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@hapi/joi": "^17.1.1", "@nestjs/common": "^7.0.0", "@nestjs/config": "^0.6.3", "@nestjs/core": "^7.6.15", "@nestjs/jwt": "^7.2.0", "@nestjs/mongoose": "^7.2.4", "@nestjs/passport": "^7.1.5", "@nestjs/platform-express": "^7.0.0", "@nestjs/schedule": "^1.0.2", "@nestjs/typeorm": "^7.1.5", "@types/cookie-parser": "^1.4.2", "@types/passport-jwt": "^3.0.5", "@types/passport-local": "^1.0.33", "axios": "^1.7.7", "bcrypt": "^5.0.1", "cookie-parser": "^1.4.5", "dotenv": "^8.2.0", "fs": "^0.0.1-security", "generate-password": "^1.6.1", "ioredis": "^4.30.0", "linq": "^3.2.4", "luxon": "^3.6.1", "mongodb": "^3.6.6", "mongoose": "^5.12.3", "nestjs": "0.0.1", "nestjs-easyconfig": "^2.4.2", "node-linq": "0.0.5", "nodemailer": "^6.6.5", "nodemon": "^2.0.7", "passport": "^0.4.1", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pg": "^8.6.0", "reflect-metadata": "^0.1.13", "request": "^2.88.2", "rimraf": "^3.0.2", "rxjs": "^6.5.4", "typeorm": "^0.2.32"}, "devDependencies": {"@nestjs/cli": "^7.0.0", "@nestjs/schematics": "^7.0.0", "@nestjs/testing": "^7.0.0", "@types/bcrypt": "^3.0.0", "@types/cron": "^1.7.3", "@types/express": "^4.17.11", "@types/jest": "25.1.4", "@types/node": "^13.9.1", "@types/passport": "^1.0.6", "@types/supertest": "^2.0.8", "@typescript-eslint/eslint-plugin": "^2.23.0", "@typescript-eslint/parser": "^2.23.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "jest": "^25.1.0", "prettier": "^1.19.1", "supertest": "^4.0.2", "ts-jest": "25.2.1", "ts-loader": "^6.2.1", "ts-node": "^8.10.2", "tsconfig-paths": "^3.9.0", "typescript": "^3.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}