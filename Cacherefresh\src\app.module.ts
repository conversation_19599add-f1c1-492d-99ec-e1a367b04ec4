import { Module } from '@nestjs/common';
/* Commented by <PERSON><PERSON>ep for removing mongo db reference*/
// import { MongooseModule } from '@nestjs/mongoose';
import { AppController } from './app.controller';
import { AppService } from './app.service';
// import { AuthModule } from './auth/auth.module';
import * as dotenv from 'dotenv';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChartBuilderModule } from './chartbuilder/chart-builder.module';
import { EasyConfiguration } from './config/easyconfig.service';
import { EasyconfigModule } from 'nestjs-easyconfig';
import { join } from 'path';
// import { SharedDashboardModule } from './shared-dashboard/shared-dashboard.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CacheRefreshModule } from './cache-refresh/cache-refresh.module';
import { AlertModule } from './alerting-agent/alert.module';
import { SubscriptionModule } from './subscription/subs.module';
import { RedisModule } from './redis/redis.module';

dotenv.config();

@Module({
  imports: [
    EasyconfigModule.register({ path: './.env' }),
    /* Commented by Sudeep for removing mongo db reference*/
    // MongooseModule.forRoot('mongodb://collectionreader:Lacritz12345#@************:27017/dclmdb', {
    //   connectionName: process.env.DB_Mongo_DATABASE1
    // }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_Postgres_HOST,
      username: process.env.DB_Postgres_USER,
      password: process.env.DB_Postgres_PASSWORD,
      database: process.env.DB_Postgres_DATABASE,
      port: parseInt(process.env.DB_Postgres_PORT, 10),
      entities: [join(__dirname, '/**/*.entity{.ts,.js}')],
      autoLoadEntities: true,
    }),
    // AuthModule,
    ChartBuilderModule,
    // SharedDashboardModule,
    ScheduleModule.forRoot(),
    CacheRefreshModule,
    AlertModule,
    SubscriptionModule,
    RedisModule,
  ],
  controllers: [AppController],
  providers: [AppService, EasyConfiguration],
})
export class AppModule {}
